import type { BaseChatModel } from '@langchain/core/language_models/chat_models';
import { StructuredOutputParser } from '@langchain/core/output_parsers';
import { ChatPromptTemplate } from '@langchain/core/prompts';
import { RunnableSequence } from '@langchain/core/runnables';
import { z } from 'zod';

import type { Job } from '../entities/job';
import type {
  ConsolidatedSkill,
  MissingSkill,
  SkillMatchOptions,
  TransferableSkillMatch,
} from './types';
import { isAIUnavailableError, isTimeoutError, withTimeout } from './utils';

/**
 * Schema for AI-powered transferable skill analysis
 */
const transferabilityAnalysisSchema = z.object({
  matches: z.array(
    z.object({
      resumeSkill: z.string(),
      jobSkill: z.string(),
      confidenceRating: z.union([z.literal(1), z.literal(2), z.literal(3)]),
      reasoning: z.string().max(200),
    })
  ),
});

/**
 * Internal interface for skill comparison pairs
 */
interface SkillComparison {
  resumeSkill: ConsolidatedSkill;
  jobSkill: NonNullable<Job['skills']>[number];
}

/**
 * Analyzes transferable skills using AI reasoning to identify similar skills
 * with confidence ratings between resume skills and job requirements.
 */
export class TransferableSkillAnalyzer {
  private readonly outputParser: StructuredOutputParser<
    typeof transferabilityAnalysisSchema
  >;
  private readonly promptTemplate: ChatPromptTemplate;

  constructor(private readonly model: BaseChatModel) {
    this.outputParser = StructuredOutputParser.fromZodSchema(
      transferabilityAnalysisSchema
    );
    this.promptTemplate = this.createTransferabilityPrompt();
  }

  /**
   * Analyzes transferable skills between unmatched resume skills and job requirements
   *
   * @param unmatchedResumeSkills - Resume skills that didn't have direct matches
   * @param unmatchedJobSkills - Job skills that didn't have direct matches
   * @param options - Configuration options for the analysis
   * @returns Promise resolving to array of transferable skill matches
   */
  async analyzeTransferableSkills(
    unmatchedResumeSkills: ConsolidatedSkill[],
    unmatchedJobSkills: Job['skills'],
    options: SkillMatchOptions = {}
  ): Promise<TransferableSkillMatch[]> {
    if (!unmatchedJobSkills?.length) {
      return [];
    }

    // Create skill comparison pairs
    const skillComparisons = this.createSkillComparisons(
      unmatchedResumeSkills,
      unmatchedJobSkills
    );

    if (!skillComparisons.length) {
      return [];
    }

    // Batch process skill comparisons for efficiency
    const batchedComparisons = this.batchSkillComparisons(skillComparisons);
    const allMatches: TransferableSkillMatch[] = [];

    for (const batch of batchedComparisons) {
      try {
        const batchMatches = await this.processBatch(batch, options);
        allMatches.push(...batchMatches);
      } catch (error) {
        if (isAIUnavailableError(error)) {
          console.warn(
            'AI model unavailable for transferable skill batch, skipping batch'
          );
        } else if (isTimeoutError(error)) {
          console.warn('Transferable skill batch timed out, skipping batch');
        } else {
          console.warn('Failed to process transferable skill batch:', error);
        }
        // Continue with other batches on failure
      }
    }

    // Apply confidence threshold and limit if specified
    return this.filterAndLimitResults(allMatches, options);
  }

  /**
   * Creates the prompt template for transferable skill analysis
   */
  private createTransferabilityPrompt(): ChatPromptTemplate {
    return ChatPromptTemplate.fromTemplate(`
You are an expert career counselor analyzing skill transferability between a candidate's resume and job requirements.

Your task is to identify which resume skills are transferable to job requirements that don't have direct matches. Consider:

1. **Technology Family Relationships**: Skills in the same technology family (e.g., React vs Vue, MySQL vs PostgreSQL) should have high transferability (confidence 2-3)

2. **Conceptual Similarity**: Skills that share similar concepts or problem-solving approaches (e.g., Java vs C#, AWS vs Azure) should have moderate to high transferability (confidence 2-3)

3. **Domain Knowledge Transfer**: Skills that require similar domain knowledge or thinking patterns (e.g., data analysis tools, testing frameworks) should have moderate transferability (confidence 1-2)

4. **Learning Curve Consideration**: Skills that require significant learning or paradigm shifts should have lower confidence ratings (confidence 1-2)

**Confidence Rating Guidelines:**
- **3 (High)**: Very similar technologies, minimal learning curve, direct transferability
- **2 (Medium)**: Related technologies, moderate learning curve, good transferability with some adaptation
- **1 (Low)**: Conceptually related but requires significant learning, basic transferability

**Resume Skills to Analyze:**
{resumeSkills}

**Job Requirements to Match Against:**
{jobSkills}

Analyze each resume skill against each job requirement and identify transferable matches. Provide clear, concise reasoning (max 200 characters) for each match.

{formatInstructions}
`);
  }

  /**
   * Creates skill comparison pairs from unmatched skills with deduplication
   */
  private createSkillComparisons(
    resumeSkills: ConsolidatedSkill[],
    jobSkills: Job['skills']
  ): SkillComparison[] {
    const comparisons: SkillComparison[] = [];
    const seenPairs = new Set<string>();

    for (const resumeSkill of resumeSkills) {
      if (!jobSkills) {
        continue;
      }

      for (const jobSkill of jobSkills) {
        // Create a normalized key for deduplication
        const pairKey = this.createSkillPairKey(
          resumeSkill.name,
          jobSkill.name
        );

        // Skip if we've already seen this skill pair
        if (seenPairs.has(pairKey)) {
          continue;
        }

        seenPairs.add(pairKey);
        comparisons.push({
          resumeSkill,
          jobSkill,
        });
      }
    }

    return this.optimizeSkillComparisons(comparisons);
  }

  /**
   * Batches skill comparisons for efficient AI processing
   * Limits batch size to avoid overwhelming the AI model
   */
  private batchSkillComparisons(
    comparisons: SkillComparison[]
  ): SkillComparison[][] {
    const BATCH_SIZE = 10; // Process up to 10 skill comparisons per AI call
    const batches: SkillComparison[][] = [];

    for (let i = 0; i < comparisons.length; i += BATCH_SIZE) {
      batches.push(comparisons.slice(i, i + BATCH_SIZE));
    }

    return batches;
  }

  /**
   * Processes a batch of skill comparisons using AI
   */
  private async processBatch(
    batch: SkillComparison[],
    options: SkillMatchOptions
  ): Promise<TransferableSkillMatch[]> {
    // Format skills for the prompt
    const resumeSkillsText = batch
      .map(
        ({ resumeSkill }) =>
          `- ${resumeSkill.name}${
            resumeSkill.keywords?.length
              ? ` (${resumeSkill.keywords.join(', ')})`
              : ''
          }`
      )
      .join('\n');

    const jobSkillsText = batch
      .map(
        ({ jobSkill }) =>
          `- ${jobSkill.name}${
            jobSkill.keywords?.length
              ? ` (${jobSkill.keywords.join(', ')})`
              : ''
          }`
      )
      .join('\n');

    // Create the runnable sequence
    const chain = RunnableSequence.from([
      this.promptTemplate,
      this.model,
      this.outputParser,
    ]);

    // Execute the AI analysis
    const chainOperation = chain.invoke({
      resumeSkills: resumeSkillsText,
      jobSkills: jobSkillsText,
      formatInstructions: this.outputParser.getFormatInstructions(),
    });

    const result = options.timeoutMs
      ? await withTimeout(
          chainOperation,
          options.timeoutMs,
          'Transferable skill AI analysis'
        )
      : await chainOperation;

    // Convert AI results to TransferableSkillMatch format with validation
    return result.matches
      .filter((match) => this.validateMatch(match, options))
      .map((match) => {
        // Find the source resume information
        const resumeSkill = batch.find(
          (b) => b.resumeSkill.name === match.resumeSkill
        )?.resumeSkill;
        const sourceResume =
          options.includeSourceResume && resumeSkill?.sourceResumes.length
            ? resumeSkill.sourceResumes[0]
            : undefined;

        return {
          jobSkill: match.jobSkill,
          resumeSkill: match.resumeSkill,
          confidenceRating: match.confidenceRating,
          reasoning: this.validateAndCleanReasoning(match.reasoning),
          sourceResume,
        };
      });
  }

  /**
   * Filters results by confidence threshold and applies limits
   */
  private filterAndLimitResults(
    matches: TransferableSkillMatch[],
    options: SkillMatchOptions
  ): TransferableSkillMatch[] {
    let filteredMatches = matches;

    // Apply confidence threshold
    if (options.confidenceThreshold) {
      filteredMatches = filteredMatches.filter((match) =>
        options.confidenceThreshold
          ? match.confidenceRating >= options.confidenceThreshold
          : true
      );
    }

    // Sort by confidence rating (highest first) and limit if specified
    filteredMatches.sort((a, b) => b.confidenceRating - a.confidenceRating);

    if (options.maxTransferableSkills) {
      filteredMatches = filteredMatches.slice(0, options.maxTransferableSkills);
    }

    return filteredMatches;
  }

  /**
   * Validates a match result from AI analysis
   */
  private validateMatch(
    match: TransferableSkillMatch,
    options: SkillMatchOptions
  ): boolean {
    // Validate confidence rating
    if (!this.isValidConfidenceRating(match.confidenceRating)) {
      console.warn(
        `Invalid confidence rating: ${match.confidenceRating} for match ${match.resumeSkill} -> ${match.jobSkill}`
      );
      return false;
    }

    // Apply confidence threshold
    if (match.confidenceRating < (options.confidenceThreshold || 1)) {
      return false;
    }

    // Validate reasoning quality
    if (!this.isValidReasoning(match.reasoning)) {
      console.warn(
        `Invalid reasoning for match ${match.resumeSkill} -> ${match.jobSkill}: ${match.reasoning}`
      );
      return false;
    }

    return true;
  }

  /**
   * Validates that confidence rating is within acceptable range
   */
  private isValidConfidenceRating(rating: unknown): rating is 1 | 2 | 3 {
    return typeof rating === 'number' && [1, 2, 3].includes(rating);
  }

  /**
   * Validates reasoning text quality
   */
  private isValidReasoning(reasoning: unknown): reasoning is string {
    if (typeof reasoning !== 'string') {
      return false;
    }

    // Check minimum length (should have some meaningful content)
    if (reasoning.trim().length < 10) {
      return false;
    }

    // Check maximum length (as per schema constraint)
    if (reasoning.length > 200) {
      return false;
    }

    // Check for placeholder or generic responses
    const genericResponses = [
      'similar',
      'related',
      'both are',
      'same category',
      'no reason',
      'n/a',
      'none',
    ];

    const lowerReasoning = reasoning.toLowerCase();
    const isGeneric = genericResponses.some(
      (generic) =>
        lowerReasoning === generic || lowerReasoning.startsWith(generic + '.')
    );

    if (isGeneric) {
      return false;
    }

    return true;
  }

  /**
   * Validates and cleans reasoning text
   */
  private validateAndCleanReasoning(reasoning: string): string {
    // Trim whitespace
    let cleaned = reasoning.trim();

    // Ensure it doesn't exceed maximum length
    if (cleaned.length > 200) {
      cleaned = cleaned.substring(0, 197) + '...';
    }

    // Ensure it starts with a capital letter
    if (cleaned.length > 0 && cleaned[0] !== cleaned[0].toUpperCase()) {
      cleaned = cleaned[0].toUpperCase() + cleaned.slice(1);
    }

    // Ensure it ends with proper punctuation
    if (cleaned.length > 0 && !cleaned.match(/[.!?]$/)) {
      cleaned += '.';
    }

    return cleaned;
  }

  /**
   * Creates a normalized key for skill pair deduplication
   */
  private createSkillPairKey(resumeSkill: string, jobSkill: string): string {
    // Normalize skill names for comparison (lowercase, trim)
    const normalizedResume = resumeSkill.toLowerCase().trim();
    const normalizedJob = jobSkill.toLowerCase().trim();

    // Create a consistent key regardless of order
    return `${normalizedResume}|${normalizedJob}`;
  }

  /**
   * Optimizes skill comparisons by grouping similar skills and prioritizing high-value comparisons
   */
  private optimizeSkillComparisons(
    comparisons: SkillComparison[]
  ): SkillComparison[] {
    // Group comparisons by similarity to reduce redundant AI calls
    const optimizedComparisons = this.groupSimilarComparisons(comparisons);

    // Sort by priority (more specific skills first, then by keyword overlap)
    return optimizedComparisons.sort((a, b) => {
      const priorityA = this.calculateComparisonPriority(a);
      const priorityB = this.calculateComparisonPriority(b);
      return priorityB - priorityA; // Higher priority first
    });
  }

  /**
   * Groups similar skill comparisons to reduce redundant AI processing
   */
  private groupSimilarComparisons(
    comparisons: SkillComparison[]
  ): SkillComparison[] {
    const grouped = new Map<string, SkillComparison>();

    for (const comparison of comparisons) {
      // Create a similarity key based on skill categories/types
      const similarityKey = this.createSimilarityKey(comparison);

      // If we haven't seen this type of comparison, add it
      if (!grouped.has(similarityKey)) {
        grouped.set(similarityKey, comparison);
      } else {
        // If we have seen similar comparison, keep the one with more keywords/context
        const existing = grouped.get(similarityKey);
        if (existing && this.hasMoreContext(comparison, existing)) {
          grouped.set(similarityKey, comparison);
        }
      }
    }

    return Array.from(grouped.values());
  }

  /**
   * Creates a similarity key for grouping related skill comparisons
   */
  private createSimilarityKey(comparison: SkillComparison): string {
    const resumeSkill = comparison.resumeSkill.name.toLowerCase();
    const jobSkill = comparison.jobSkill.name.toLowerCase();

    // Group by technology categories
    const resumeCategory = this.getSkillCategory(resumeSkill);
    const jobCategory = this.getSkillCategory(jobSkill);

    return `${resumeCategory}-${jobCategory}`;
  }

  /**
   * Determines the category of a skill for grouping purposes
   */
  private getSkillCategory(skillName: string): string {
    const skill = skillName.toLowerCase();

    // Frontend frameworks
    if (
      ['react', 'vue', 'angular', 'svelte', 'ember'].some((fw) =>
        skill.includes(fw)
      )
    ) {
      return 'frontend-framework';
    }

    // Backend frameworks
    if (
      [
        'express',
        'fastify',
        'koa',
        'nest',
        'spring',
        'django',
        'flask',
        'rails',
      ].some((fw) => skill.includes(fw))
    ) {
      return 'backend-framework';
    }

    // Databases
    if (
      [
        'mysql',
        'postgresql',
        'mongodb',
        'redis',
        'sqlite',
        'oracle',
        'sql',
      ].some((db) => skill.includes(db))
    ) {
      return 'database';
    }

    // Programming languages
    if (
      [
        'javascript',
        'typescript',
        'python',
        'java',
        'c#',
        'go',
        'rust',
        'php',
        'ruby',
      ].some((lang) => skill.includes(lang))
    ) {
      return 'programming-language';
    }

    // Cloud platforms
    if (
      ['aws', 'azure', 'gcp', 'google cloud', 'cloudflare'].some((cloud) =>
        skill.includes(cloud)
      )
    ) {
      return 'cloud-platform';
    }

    // Testing tools
    if (
      ['jest', 'vitest', 'cypress', 'playwright', 'selenium', 'junit'].some(
        (test) => skill.includes(test)
      )
    ) {
      return 'testing-tool';
    }

    // Default category
    return 'general';
  }

  /**
   * Determines if one comparison has more context than another
   */
  private hasMoreContext(
    comparison1: SkillComparison,
    comparison2: SkillComparison
  ): boolean {
    const context1 =
      (comparison1.resumeSkill.keywords?.length || 0) +
      (comparison1.jobSkill.keywords?.length || 0);
    const context2 =
      (comparison2.resumeSkill.keywords?.length || 0) +
      (comparison2.jobSkill.keywords?.length || 0);

    return context1 > context2;
  }

  /**
   * Calculates priority score for a skill comparison
   */
  private calculateComparisonPriority(comparison: SkillComparison): number {
    let priority = 0;

    // Higher priority for skills with more keywords (more specific)
    priority += (comparison.resumeSkill.keywords?.length || 0) * 2;
    priority += (comparison.jobSkill.keywords?.length || 0) * 2;

    // Higher priority for exact keyword overlap
    const resumeKeywords = comparison.resumeSkill.keywords || [];
    const jobKeywords = comparison.jobSkill.keywords || [];
    const keywordOverlap = resumeKeywords.filter((rk) =>
      jobKeywords.some((jk) => jk.toLowerCase() === rk.toLowerCase())
    ).length;
    priority += keywordOverlap * 5;

    // Higher priority for skills in the same category
    const resumeCategory = this.getSkillCategory(comparison.resumeSkill.name);
    const jobCategory = this.getSkillCategory(comparison.jobSkill.name);
    if (resumeCategory === jobCategory && resumeCategory !== 'general') {
      priority += 3;
    }

    return priority;
  }

  /**
   * Identifies missing skills that the candidate doesn't have (neither direct nor transferable matches)
   *
   * @param jobSkills - All job skills required
   * @param directMatches - Skills that had direct matches
   * @param transferableMatches - Skills that had transferable matches
   * @returns Array of missing skills with categorization for learning recommendations
   */
  identifyMissingSkills(
    jobSkills: Job['skills'],
    directMatches: string[],
    transferableMatches: string[]
  ): MissingSkill[] {
    if (!jobSkills?.length) {
      return [];
    }

    const matchedSkills = new Set([
      ...directMatches.map((skill) => skill.toLowerCase()),
      ...transferableMatches.map((skill) => skill.toLowerCase()),
    ]);

    return jobSkills
      .filter((jobSkill) => !matchedSkills.has(jobSkill.name.toLowerCase()))
      .map((jobSkill) => ({
        name: jobSkill.name,
        level: jobSkill.level,
        keywords: jobSkill.keywords,
        category: this.getSkillCategory(jobSkill.name),
      }));
  }
}
