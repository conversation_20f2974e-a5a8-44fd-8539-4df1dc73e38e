import type { BaseChatModel } from '@langchain/core/language_models/chat_models';

import type { Job } from '../entities/job';
import type { Resume } from '../entities/resume';
import { DirectMatchAnalyzer } from './direct-match-analyzer';
import { ResultAggregator } from './result-aggregator';
import { SkillConsolidator } from './skill-consolidator';
import { TransferableSkillAnalyzer } from './transferable-skill-analyzer';
import type {
  SkillMatchAnalysis,
  SkillMatchOptions,
  TransferableSkillMatch,
} from './types';
import { isAIUnavailableError, isTimeoutError, withTimeout } from './utils';

/**
 * Error class for skill matching operations
 */
export class SkillMatchError extends Error {
  constructor(
    message: string,
    public readonly code: 'AI_UNAVAILABLE' | 'TIMEOUT' | 'INVALID_INPUT',
    public readonly partialResults?: Partial<SkillMatchAnalysis>
  ) {
    super(message);
    this.name = 'SkillMatchError';
  }
}

/**
 * Analyzes skill matches between multiple resume variations and a job posting.
 *
 * This function consolidates skills from all resume variations, performs direct
 * matching (exact, synonym, and keyword-based), and uses AI reasoning to identify
 * transferable skills with confidence ratings.
 *
 * @param resumeVariations - Array of resume variations for the same person
 * @param job - Job posting to match against
 * @param model - LangChain BaseChatModel for AI-powered analysis
 * @param options - Optional configuration for the analysis
 * @returns Promise resolving to comprehensive skill match analysis
 *
 * @example
 * ```typescript
 * const analysis = await analyzeSkillMatch(
 *   [resume1, resume2],
 *   jobPosting,
 *   mistralModel,
 *   { includeSourceResume: true, confidenceThreshold: 2 }
 * );
 *
 * console.log(`Direct matches: ${analysis.directMatches.length}`);
 * console.log(`Coverage: ${analysis.summary.coveragePercentage}%`);
 * ```
 */
export async function analyzeSkillMatch(
  resumeVariations: Resume[],
  job: Job,
  model: BaseChatModel,
  options: SkillMatchOptions = {}
): Promise<SkillMatchAnalysis> {
  validateInputs(resumeVariations, job, model);

  const finalOptions = {
    includeSourceResume: false,
    maxTransferableSkills: 20,
    confidenceThreshold: 1,
    timeoutMs: 30000, // 30 seconds default timeout
    ...options,
  } satisfies SkillMatchOptions;

  let directMatches: Awaited<
    ReturnType<DirectMatchAnalyzer['findDirectMatches']>
  > = [];

  try {
    // Step 1: Consolidate skills from all resume variations
    const skillConsolidator = new SkillConsolidator();
    const consolidatedSkills = skillConsolidator.consolidate(resumeVariations);

    // Step 2: Find direct matches (exact, synonym, keyword)
    const directMatchAnalyzer = new DirectMatchAnalyzer(model);
    directMatches = await withTimeout(
      directMatchAnalyzer.findDirectMatches(consolidatedSkills, job.skills, {
        includeSourceResume: finalOptions.includeSourceResume,
        timeoutMs: finalOptions.timeoutMs,
      }),
      finalOptions.timeoutMs,
      'Direct match analysis'
    );

    // Step 3: Identify unmatched skills for transferable analysis
    const { unmatchedResumeSkills, unmatchedJobSkills } =
      identifyUnmatchedSkills(consolidatedSkills, job.skills, directMatches);

    // Step 4: Analyze transferable skills using AI
    let transferableSkills: TransferableSkillMatch[] = [];
    try {
      const transferableSkillAnalyzer = new TransferableSkillAnalyzer(model);
      transferableSkills = await withTimeout(
        transferableSkillAnalyzer.analyzeTransferableSkills(
          unmatchedResumeSkills,
          unmatchedJobSkills,
          finalOptions
        ),
        finalOptions.timeoutMs!,
        'Transferable skill analysis'
      );
    } catch (error) {
      // Graceful degradation: continue without transferable skills if AI fails
      console.warn(
        'AI analysis failed, continuing with direct matches only:',
        error
      );

      // If AI is completely unavailable, provide partial results but don't throw
      if (isAIUnavailableError(error)) {
        console.warn(
          'AI model unavailable, falling back to direct matches only'
        );
        // Continue with empty transferable skills array
      } else if (isTimeoutError(error)) {
        console.warn(
          'AI analysis timed out, falling back to direct matches only'
        );
        // Continue with empty transferable skills array
      } else {
        console.warn(
          'Unexpected error in AI analysis, falling back to direct matches only:',
          error
        );
        // Continue with empty transferable skills array
      }
    }

    // Step 5: Aggregate results into final analysis
    const resultAggregator = new ResultAggregator();
    const analysis = resultAggregator.aggregate(
      directMatches,
      transferableSkills,
      job.skills,
      finalOptions
    );

    return analysis;
  } catch (error) {
    // Handle known errors
    if (error instanceof SkillMatchError) {
      throw error;
    }

    // Handle timeout errors
    if (isTimeoutError(error)) {
      // Provide partial results if we have direct matches
      const partialResults = directMatches
        ? createPartialResults(directMatches, job.skills, finalOptions)
        : undefined;

      throw new SkillMatchError(
        'Skill matching analysis timed out. Try reducing the number of resume variations or increasing the timeout.',
        'TIMEOUT',
        partialResults
      );
    }

    // Handle AI unavailability
    if (isAIUnavailableError(error)) {
      // Provide partial results if we have direct matches
      const partialResults = directMatches
        ? createPartialResults(directMatches, job.skills, finalOptions)
        : undefined;

      throw new SkillMatchError(
        'AI model is unavailable. Only direct skill matching could be performed.',
        'AI_UNAVAILABLE',
        partialResults
      );
    }

    // Handle unexpected errors
    throw new SkillMatchError(
      `Unexpected error during skill matching: ${
        error instanceof Error ? error.message : 'Unknown error'
      }`,
      'INVALID_INPUT'
    );
  }
}

/**
 * Validates input parameters for the analyzeSkillMatch function
 */
function validateInputs(
  resumeVariations: Resume[],
  job: Job,
  model: BaseChatModel
): void {
  // Validate resume variations
  if (!Array.isArray(resumeVariations)) {
    throw new SkillMatchError(
      'resumeVariations must be an array',
      'INVALID_INPUT'
    );
  }

  if (resumeVariations.length === 0) {
    throw new SkillMatchError(
      'At least one resume variation is required',
      'INVALID_INPUT'
    );
  }

  // Validate each resume has the required structure
  for (let i = 0; i < resumeVariations.length; i++) {
    const resume = resumeVariations[i];
    if (!resume || typeof resume !== 'object') {
      throw new SkillMatchError(
        `Resume variation at index ${i} is invalid`,
        'INVALID_INPUT'
      );
    }

    if (!Array.isArray(resume.sections)) {
      throw new SkillMatchError(
        `Resume variation at index ${i} must have a sections array`,
        'INVALID_INPUT'
      );
    }
  }

  // Validate job object
  if (!job || typeof job !== 'object') {
    throw new SkillMatchError(
      'job must be a valid Job object',
      'INVALID_INPUT'
    );
  }

  // Validate job has skills array (can be empty or null but if present must be an array)
  if (
    job.skills !== null &&
    job.skills !== undefined &&
    !Array.isArray(job.skills)
  ) {
    throw new SkillMatchError(
      'job.skills must be an array or null',
      'INVALID_INPUT'
    );
  }

  // Validate job skills structure if skills array exists
  if (job.skills && Array.isArray(job.skills)) {
    for (let i = 0; i < job.skills.length; i++) {
      const skill = job.skills[i];
      if (!skill || typeof skill !== 'object' || !skill.name) {
        throw new SkillMatchError(
          `Job skill at index ${i} must have a name property`,
          'INVALID_INPUT'
        );
      }
    }
  }

  // Validate model
  if (!model) {
    throw new SkillMatchError(
      'model is required for AI-powered analysis',
      'INVALID_INPUT'
    );
  }

  // Validate that model has required methods
  if (typeof model.invoke !== 'function') {
    throw new SkillMatchError(
      'model must be a valid LangChain BaseChatModel',
      'INVALID_INPUT'
    );
  }
}

/**
 * Identifies skills that didn't have direct matches for transferable analysis
 */
function identifyUnmatchedSkills(
  consolidatedSkills: ReturnType<SkillConsolidator['consolidate']>,
  jobSkills: Job['skills'],
  directMatches: Awaited<ReturnType<DirectMatchAnalyzer['findDirectMatches']>>
) {
  // Create sets of matched skills for efficient lookup
  const matchedResumeSkills = new Set(
    directMatches.map((match) => match.resumeSkill.toLowerCase().trim())
  );

  const matchedJobSkills = new Set(
    directMatches.map((match) => match.jobSkill.toLowerCase().trim())
  );

  // Find unmatched resume skills
  const unmatchedResumeSkills = consolidatedSkills.filter(
    (skill) => !matchedResumeSkills.has(skill.name.toLowerCase().trim())
  );

  // Find unmatched job skills
  const unmatchedJobSkills =
    jobSkills?.filter(
      (skill) => !matchedJobSkills.has(skill.name.toLowerCase().trim())
    ) || [];

  return {
    unmatchedResumeSkills,
    unmatchedJobSkills,
  };
}

/**
 * Creates partial results when AI analysis fails
 */
function createPartialResults(
  directMatches: Awaited<ReturnType<DirectMatchAnalyzer['findDirectMatches']>>,
  jobSkills: Job['skills'],
  options: SkillMatchOptions
): Partial<SkillMatchAnalysis> {
  const resultAggregator = new ResultAggregator();
  return resultAggregator.aggregate(
    directMatches,
    [], // No transferable skills due to AI failure
    jobSkills,
    options
  );
}
