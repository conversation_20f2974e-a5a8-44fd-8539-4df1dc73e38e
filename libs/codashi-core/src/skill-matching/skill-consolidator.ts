import type { Resume } from '../entities/resume';
import type { ConsolidatedSkill } from './types';

/**
 * Consolidates skills from multiple resume variations into a unified list.
 *
 * This class extracts skills from all resume sections (skills, work, projects)
 * and merges duplicate skills across resume variations while tracking their sources.
 */
export class SkillConsolidator {
  /**
   * Consolidates skills from multiple resume variations.
   *
   * @param resumeVariations - Array of resume variations to consolidate skills from
   * @returns Array of consolidated skills with source tracking
   */
  consolidate(resumeVariations: Resume[]): ConsolidatedSkill[] {
    const skillMap = new Map<string, ConsolidatedSkill>();

    resumeVariations.forEach((resume, resumeIndex) => {
      const extractedSkills = this.extractSkillsFromResume(resume, resumeIndex);

      extractedSkills.forEach((skill) => {
        const normalizedName = this.normalizeSkillName(skill.name);

        if (skillMap.has(normalizedName)) {
          // Merge with existing skill
          const existing = skillMap.get(normalizedName);

          if (existing) {
            this.mergeSkills(existing, skill);
          }
        } else {
          // Add new skill with normalized name
          skillMap.set(normalizedName, {
            ...skill,
            name: normalizedName,
          });
        }
      });
    });

    return Array.from(skillMap.values());
  }

  /**
   * Extracts skills from all sections of a single resume.
   *
   * @param resume - Resume to extract skills from
   * @param resumeIndex - Index of the resume in the variations array
   * @returns Array of skills found in the resume
   */
  private extractSkillsFromResume(
    resume: Resume,
    resumeIndex: number
  ): ConsolidatedSkill[] {
    const skills: ConsolidatedSkill[] = [];

    // Extract from skills section
    const skillsSection = resume.sections.find(
      (section) => section.name === 'skills'
    );
    if (skillsSection) {
      skillsSection.items.forEach((skill) => {
        skills.push({
          name: skill.name,
          level: skill.level || undefined,
          keywords: skill.keywords || [],
          sourceResumes: [resumeIndex],
        });

        // Also add keywords as separate skills if they exist
        if (skill.keywords) {
          skill.keywords.forEach((keyword) => {
            skills.push({
              name: keyword,
              level: undefined,
              keywords: [],
              sourceResumes: [resumeIndex],
            });
          });
        }
      });
    }

    // Extract from work section
    const workSection = resume.sections.find(
      (section) => section.name === 'work'
    );
    if (workSection) {
      workSection.items.forEach((workItem) => {
        // Extract skills from work highlights (bullet points)
        if (workItem.highlights) {
          const workSkills = this.extractSkillsFromText(
            workItem.highlights.join(' ')
          );
          workSkills.forEach((skillName) => {
            skills.push({
              name: skillName,
              level: undefined,
              keywords: [],
              sourceResumes: [resumeIndex],
            });
          });
        }

        // Extract skills from work summary
        if (workItem.summary) {
          const summarySkills = this.extractSkillsFromText(workItem.summary);
          summarySkills.forEach((skillName) => {
            skills.push({
              name: skillName,
              level: undefined,
              keywords: [],
              sourceResumes: [resumeIndex],
            });
          });
        }
      });
    }

    // Extract from projects section
    const projectsSection = resume.sections.find(
      (section) => section.name === 'projects'
    );
    if (projectsSection) {
      projectsSection.items.forEach((project) => {
        // Extract from project keywords
        if (project.keywords) {
          project.keywords.forEach((keyword) => {
            skills.push({
              name: keyword,
              level: undefined,
              keywords: [],
              sourceResumes: [resumeIndex],
            });
          });
        }

        // Extract skills from project highlights
        if (project.highlights) {
          const projectSkills = this.extractSkillsFromText(
            project.highlights.join(' ')
          );
          projectSkills.forEach((skillName) => {
            skills.push({
              name: skillName,
              level: undefined,
              keywords: [],
              sourceResumes: [resumeIndex],
            });
          });
        }

        // Extract skills from project description
        const descriptionSkills = this.extractSkillsFromText(
          project.description
        );
        descriptionSkills.forEach((skillName) => {
          skills.push({
            name: skillName,
            level: undefined,
            keywords: [],
            sourceResumes: [resumeIndex],
          });
        });
      });
    }

    return skills;
  }

  /**
   * Normalizes skill names for consistent matching.
   *
   * @param skillName - Raw skill name to normalize
   * @returns Normalized skill name
   */
  private normalizeSkillName(skillName: string): string {
    return skillName
      .toLowerCase()
      .trim()
      .replace(/[^\w\s.+-]/g, '') // Remove special characters except dots, hyphens, plus signs, and spaces
      .replace(/\s+/g, ' '); // Normalize whitespace
  }

  /**
   * Merges two skills with the same normalized name.
   *
   * @param existing - Existing consolidated skill
   * @param newSkill - New skill to merge
   */
  private mergeSkills(
    existing: ConsolidatedSkill,
    newSkill: ConsolidatedSkill
  ): void {
    // Merge source resumes
    newSkill.sourceResumes.forEach((resumeIndex) => {
      if (!existing.sourceResumes.includes(resumeIndex)) {
        existing.sourceResumes.push(resumeIndex);
      }
    });

    // Use the highest level if available
    if (
      newSkill.level &&
      (!existing.level ||
        this.compareLevels(newSkill.level, existing.level) > 0)
    ) {
      existing.level = newSkill.level;
    }

    // Merge keywords
    newSkill.keywords.forEach((keyword) => {
      const normalizedKeyword = this.normalizeSkillName(keyword);
      const existingNormalizedKeywords = existing.keywords.map((k) =>
        this.normalizeSkillName(k)
      );

      if (!existingNormalizedKeywords.includes(normalizedKeyword)) {
        existing.keywords.push(keyword);
      }
    });
  }

  /**
   * Compares skill levels to determine which is higher.
   *
   * @param level1 - First level to compare
   * @param level2 - Second level to compare
   * @returns Positive if level1 > level2, negative if level1 < level2, 0 if equal
   */
  private compareLevels(level1: string, level2: string): number {
    const levelOrder = [
      'beginner',
      'intermediate',
      'advanced',
      'expert',
      'master',
    ];
    const index1 = levelOrder.indexOf(level1.toLowerCase());
    const index2 = levelOrder.indexOf(level2.toLowerCase());

    // If levels are not in our known order, treat them as equal
    if (index1 === -1 || index2 === -1) {
      return 0;
    }

    return index1 - index2;
  }

  /**
   * Extracts potential skill names from free text.
   * This is a basic implementation that looks for common technology patterns.
   *
   * @param text - Text to extract skills from
   * @returns Array of potential skill names found in the text
   */
  private extractSkillsFromText(text: string): string[] {
    const skills: string[] = [];

    // Common technology patterns to look for
    const techPatterns = [
      // Programming languages
      /\b(JavaScript|TypeScript|Python|Java|C\+\+|C#|PHP|Ruby|Go|Rust|Swift|Kotlin|Scala|R|MATLAB)\b/gi,
      // Web technologies
      /\b(React|Vue|Angular|Node\.js|Express|Django|Flask|Laravel|Spring|ASP\.NET)\b/gi,
      // Databases
      /\b(MySQL|PostgreSQL|MongoDB|Redis|SQLite|Oracle|SQL Server|DynamoDB|Cassandra)\b/gi,
      // Cloud platforms
      /\b(AWS|Azure|Google Cloud|GCP|Docker|Kubernetes|Terraform|Jenkins)\b/gi,
      // Tools and frameworks
      /\b(Git|GitHub|GitLab|Jira|Confluence|Slack|Figma|Adobe|Photoshop|Illustrator)\b/gi,
    ];

    techPatterns.forEach((pattern) => {
      const matches = text.match(pattern);
      if (matches) {
        matches.forEach((match) => {
          const normalized = match.trim();
          if (normalized && !skills.includes(normalized)) {
            skills.push(normalized);
          }
        });
      }
    });

    return skills;
  }
}
