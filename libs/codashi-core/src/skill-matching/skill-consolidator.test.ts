import { type IsoDate } from '@awe/core';
import { describe, expect, it } from 'vitest';
import type { Resume, ResumeSection } from '../entities/resume';
import { SkillConsolidator } from './skill-consolidator';

describe('SkillConsolidator', () => {
  const consolidator = new SkillConsolidator();

  const createMockResume = (
    skills: Array<{ name: string; level?: string; keywords?: string[] }> = [],
    workHighlights: string[] = [],
    projectKeywords: string[] = []
  ): Resume =>
    ({
      $schema: null,
      meta: {
        canonical: null,
        version: null,
        last_modified: null,
        job_description: null,
        created_at: new Date().toISOString() as IsoDate,
      },
      header: {
        items: [
          {
            name: 'name',
            value: 'Test User',
          },
        ],
      },
      sections: [
        {
          name: 'skills',
          items: skills.map((skill) => ({
            name: skill.name,
            level: skill.level || null,
            keywords: skill.keywords || null,
          })),
        } as ResumeSection<'skills'>,
        {
          name: 'work' as const,
          items:
            workHighlights.length > 0
              ? [
                  {
                    name: 'Test Company',
                    location: null,
                    description: null,
                    position: 'Developer',
                    url: null,
                    start_date: '2023-01-01',
                    end_date: null,
                    summary: null,
                    highlights: workHighlights,
                  },
                ]
              : [],
        } as ResumeSection<'work'>,
        {
          name: 'projects',
          items:
            projectKeywords.length > 0
              ? [
                  {
                    name: 'Test Project',
                    description: 'A test project using various technologies',
                    highlights: null,
                    keywords: projectKeywords,
                    start_date: '2023-01-01',
                    end_date: null,
                    url: null,
                    roles: null,
                    entity: null,
                    type: 'Personal',
                  },
                ]
              : [],
        } as ResumeSection<'projects'>,
      ],
    } as const);

  describe('consolidate', () => {
    it('should extract skills from skills section', () => {
      const resume = createMockResume([
        { name: 'JavaScript', level: 'Expert', keywords: ['React', 'Node.js'] },
        { name: 'Python', level: 'Intermediate' },
      ]);

      const result = consolidator.consolidate([resume]);

      expect(result).toHaveLength(4); // JavaScript, Python, React, Node.js

      const jsSkill = result.find((skill) => skill.name === 'javascript');
      expect(jsSkill).toBeDefined();
      expect(jsSkill?.level).toBe('Expert');
      expect(jsSkill?.sourceResumes).toEqual([0]);

      const reactSkill = result.find((skill) => skill.name === 'react');
      expect(reactSkill).toBeDefined();
      expect(reactSkill?.sourceResumes).toEqual([0]);
    });

    it('should normalize skill names', () => {
      const resume = createMockResume([
        { name: '  JavaScript  ' },
        { name: 'Node.JS' },
        { name: 'C++' },
      ]);

      const result = consolidator.consolidate([resume]);

      const skillNames = result.map((skill) => skill.name).sort();
      expect(skillNames).toEqual(['c++', 'javascript', 'node.js']);
    });

    it('should merge duplicate skills across resume variations', () => {
      const resume1 = createMockResume([
        { name: 'JavaScript', level: 'Intermediate', keywords: ['React'] },
      ]);

      const resume2 = createMockResume([
        { name: 'javascript', level: 'Expert', keywords: ['Vue'] },
      ]);

      const result = consolidator.consolidate([resume1, resume2]);

      const jsSkill = result.find((skill) => skill.name === 'javascript');
      expect(jsSkill).toBeDefined();
      expect(jsSkill?.level).toBe('Expert'); // Should use higher level
      expect(jsSkill?.sourceResumes).toEqual([0, 1]);
      expect(jsSkill?.keywords).toContain('React');
      expect(jsSkill?.keywords).toContain('Vue');
    });

    it('should extract skills from work highlights', () => {
      const resume = createMockResume(
        [],
        [
          'Built applications using React and TypeScript',
          'Worked with PostgreSQL database',
        ]
      );

      const result = consolidator.consolidate([resume]);

      const skillNames = result.map((skill) => skill.name);
      expect(skillNames).toContain('react');
      expect(skillNames).toContain('typescript');
      expect(skillNames).toContain('postgresql');
    });

    it('should extract skills from project keywords', () => {
      const resume = createMockResume([], [], ['Python', 'Django', 'MongoDB']);

      const result = consolidator.consolidate([resume]);

      const skillNames = result.map((skill) => skill.name);
      expect(skillNames).toContain('python');
      expect(skillNames).toContain('django');
      expect(skillNames).toContain('mongodb');
    });

    it('should track source resumes correctly', () => {
      const resume1 = createMockResume([{ name: 'JavaScript' }]);
      const resume2 = createMockResume([{ name: 'Python' }]);
      const resume3 = createMockResume([{ name: 'JavaScript' }]);

      const result = consolidator.consolidate([resume1, resume2, resume3]);

      const jsSkill = result.find((skill) => skill.name === 'javascript');
      const pythonSkill = result.find((skill) => skill.name === 'python');

      expect(jsSkill?.sourceResumes).toEqual([0, 2]);
      expect(pythonSkill?.sourceResumes).toEqual([1]);
    });

    it('should handle empty resume variations', () => {
      const result = consolidator.consolidate([]);
      expect(result).toEqual([]);
    });

    it('should handle resumes with no skills', () => {
      const resume = createMockResume();
      const result = consolidator.consolidate([resume]);
      expect(result).toEqual([]);
    });

    it('should merge keywords without duplicates', () => {
      const resume1 = createMockResume([
        { name: 'JavaScript', keywords: ['React', 'Node.js'] },
      ]);

      const resume2 = createMockResume([
        { name: 'JavaScript', keywords: ['react', 'Vue'] }, // 'react' should not duplicate
      ]);

      const result = consolidator.consolidate([resume1, resume2]);

      const jsSkill = result.find((skill) => skill.name === 'javascript');
      expect(jsSkill?.keywords).toHaveLength(3); // React, Node.js, Vue (no duplicate react)
    });
  });
});
